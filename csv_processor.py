#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV文件处理模块
处理CSV文件的导入、导出、验证等功能
"""

import pandas as pd
import os
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging
from data_models import get_data_model, load_csv_file, save_csv_file, auto_map_columns
from data_allocator import create_allocator
from error_handler import DataValidator, ErrorHandler, validate_and_clean_data

logger = logging.getLogger(__name__)

class CSVProcessor:
    """CSV文件处理器"""
    
    def __init__(self, mode: str):
        """
        初始化CSV处理器
        
        Args:
            mode: 处理模式，'user' 或 'link'
        """
        self.mode = mode
        self.data_model = get_data_model(mode)
        self.allocator = create_allocator()
        
        # 数据存储
        self.mother_data = None
        self.child_data_list = []
        self.merged_data = None
    
    @ErrorHandler.log_operation("导入母体文件")
    def import_mother_file(self, file_path: str) -> <PERSON><PERSON>[bool, str]:
        """
        导入母体文件

        Args:
            file_path: 文件路径

        Returns:
            (成功标志, 消息)
        """
        try:
            # 加载文件
            df, load_message = load_csv_file(file_path)
            if df is None:
                return False, load_message

            # 自动映射列名
            mapped_df, mapping_warnings = auto_map_columns(df, self.data_model.mother_columns, self.mode)
            if mapped_df is None:
                return False, f"列映射失败: {'; '.join(mapping_warnings)}"

            # 验证数据值
            is_valid, message = DataValidator.validate_data_values(mapped_df, self.mode)
            if not is_valid:
                return False, f"数据值验证失败: {message}"

            # 清理数据
            cleaned_df, clean_warnings = validate_and_clean_data(mapped_df, self.mode)

            # 验证清理后的数据
            is_valid, message = self.data_model.validate_mother_data(cleaned_df)
            if not is_valid:
                return False, f"数据格式验证失败: {message}"

            # 保存母体数据
            self.mother_data = cleaned_df.copy()
            logger.info(f"成功导入母体文件: {file_path}, 数据行数: {len(cleaned_df)}")

            # 合并所有警告信息
            all_warnings = mapping_warnings + clean_warnings

            success_msg = f"成功导入母体文件，共 {len(cleaned_df)} 条数据"
            if all_warnings:
                success_msg += f"\n提示: {'; '.join(all_warnings)}"

            return True, success_msg

        except Exception as e:
            error_msg = f"导入母体文件失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    @ErrorHandler.log_operation("导入子体文件")
    def import_child_file(self, file_path: str) -> Tuple[bool, str]:
        """
        导入子体文件

        Args:
            file_path: 文件路径

        Returns:
            (成功标志, 消息)
        """
        try:
            # 加载文件
            df, load_message = load_csv_file(file_path)
            if df is None:
                return False, load_message

            # 自动映射列名
            mapped_df, mapping_warnings = auto_map_columns(df, self.data_model.child_columns, self.mode)
            if mapped_df is None:
                return False, f"列映射失败: {'; '.join(mapping_warnings)}"

            # 验证数据值
            is_valid, message = DataValidator.validate_data_values(mapped_df, self.mode)
            if not is_valid:
                return False, f"数据值验证失败: {message}"

            # 清理数据
            cleaned_df, clean_warnings = validate_and_clean_data(mapped_df, self.mode)

            # 验证清理后的数据
            is_valid, message = self.data_model.validate_child_data(cleaned_df)
            if not is_valid:
                return False, f"数据格式验证失败: {message}"

            # 添加到子体数据列表
            self.child_data_list.append({
                'file_path': file_path,
                'data': cleaned_df.copy(),
                'file_name': Path(file_path).name
            })

            logger.info(f"成功导入子体文件: {file_path}, 数据行数: {len(cleaned_df)}")

            # 合并所有警告信息
            all_warnings = mapping_warnings + clean_warnings

            success_msg = f"成功导入子体文件 {Path(file_path).name}，共 {len(cleaned_df)} 条数据"
            if all_warnings:
                success_msg += f"\n提示: {'; '.join(all_warnings)}"

            return True, success_msg

        except Exception as e:
            error_msg = f"导入子体文件失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def merge_and_allocate_data(self) -> Tuple[bool, str]:
        """
        合并子体数据到母体并分配ID
        
        Returns:
            (成功标志, 消息)
        """
        try:
            # 如果没有母体数据，创建空的母体数据
            if self.mother_data is None:
                self.mother_data = self.data_model.create_empty_mother_data()
            
            # 开始合并
            merged_data = self.mother_data.copy()
            total_new_records = 0
            
            # 逐个合并子体数据
            for child_info in self.child_data_list:
                child_data = child_info['data']
                old_count = len(merged_data)
                
                # 合并数据
                merged_data = self.data_model.merge_child_to_mother(merged_data, child_data)
                new_count = len(merged_data)
                added_count = new_count - old_count
                total_new_records += added_count
                
                logger.info(f"合并文件 {child_info['file_name']}: 新增 {added_count} 条记录")
            
            # 如果有新数据，进行ID分配
            if total_new_records > 0:
                merged_data = self.allocator.allocate_new_data(merged_data)
                logger.info(f"完成ID分配，共分配 {total_new_records} 条新记录")
            
            # 保存合并后的数据
            self.merged_data = merged_data
            
            # 获取分配统计
            stats = self.allocator.get_allocation_statistics(merged_data)
            stats_msg = self._format_allocation_stats(stats)
            
            success_msg = f"数据合并完成！\n总记录数: {len(merged_data)}\n新增记录数: {total_new_records}\n\n分配统计:\n{stats_msg}"
            
            return True, success_msg
            
        except Exception as e:
            error_msg = f"数据合并失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    @ErrorHandler.log_operation("导出母体数据")
    def export_mother_data(self, file_path: str) -> Tuple[bool, str]:
        """
        导出母体数据

        Args:
            file_path: 导出文件路径

        Returns:
            (成功标志, 消息)
        """
        try:
            if self.merged_data is None or self.merged_data.empty:
                return False, "没有可导出的数据"

            # 保存文件
            success, message = save_csv_file(self.merged_data, file_path)
            if not success:
                return False, message

            return True, f"成功导出母体数据到 {Path(file_path).name}，共 {len(self.merged_data)} 条记录"

        except Exception as e:
            error_msg = f"导出母体数据失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    @ErrorHandler.log_operation("导出用户任务")
    def export_user_tasks(self, user_id: int, file_path: str, limit: int = None) -> Tuple[bool, str]:
        """
        导出指定用户的任务数据

        Args:
            user_id: 用户ID
            file_path: 导出文件路径
            limit: 限制导出的任务数量，None表示导出所有

        Returns:
            (成功标志, 消息)
        """
        try:
            if self.merged_data is None or self.merged_data.empty:
                return False, "没有可导出的数据"

            # 验证用户ID
            if user_id not in [1, 2, 3, 4, 5]:
                return False, f"无效的用户ID: {user_id}"

            # 筛选指定用户且未使用的数据
            user_tasks = self.merged_data[
                (self.merged_data['id'] == user_id) &
                (self.merged_data['used'] == 0)
            ].copy()

            if user_tasks.empty:
                return False, f"用户ID {user_id} 没有可领取的任务"

            # 如果设置了数量限制，则只取前N条
            if limit is not None and limit > 0:
                if limit > len(user_tasks):
                    limit = len(user_tasks)
                user_tasks = user_tasks.head(limit)

            # 根据模式选择导出列
            if self.mode == 'user':
                export_data = user_tasks[['username', 'fullname']].copy()
            else:  # link mode
                export_data = user_tasks[['url']].copy()

            # 保存文件
            success, message = save_csv_file(export_data, file_path)
            if not success:
                return False, message

            return True, f"成功导出用户ID {user_id} 的任务数据到 {Path(file_path).name}，共 {len(export_data)} 条记录"

        except Exception as e:
            error_msg = f"导出用户任务失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def get_data_summary(self) -> Dict[str, Any]:
        """获取数据摘要信息"""
        summary = {
            'has_mother_data': self.mother_data is not None and not self.mother_data.empty,
            'mother_data_count': len(self.mother_data) if self.mother_data is not None else 0,
            'child_files_count': len(self.child_data_list),
            'child_data_total': sum(len(child['data']) for child in self.child_data_list),
            'has_merged_data': self.merged_data is not None and not self.merged_data.empty,
            'merged_data_count': len(self.merged_data) if self.merged_data is not None else 0,
            'child_files': [child['file_name'] for child in self.child_data_list]
        }
        
        # 如果有合并数据，添加分配统计
        if self.merged_data is not None and not self.merged_data.empty:
            stats = self.allocator.get_allocation_statistics(self.merged_data)
            summary['allocation_stats'] = stats
        
        return summary
    
    def clear_child_data(self):
        """清空子体数据"""
        self.child_data_list = []
        logger.info("已清空所有子体数据")
    
    def clear_all_data(self):
        """清空所有数据"""
        self.mother_data = None
        self.child_data_list = []
        self.merged_data = None
        logger.info("已清空所有数据")

    @ErrorHandler.log_operation("标记任务完成")
    def mark_tasks_completed(self, user_id: int, exported_data: pd.DataFrame) -> Tuple[bool, str]:
        """
        标记导出的任务为已完成

        Args:
            user_id: 用户ID
            exported_data: 已导出的任务数据

        Returns:
            (成功标志, 消息)
        """
        try:
            if self.merged_data is None or self.merged_data.empty:
                return False, "没有可更新的数据"

            if exported_data is None or exported_data.empty:
                return False, "没有需要标记的任务数据"

            # 验证用户ID
            if user_id not in [1, 2, 3, 4, 5]:
                return False, f"无效的用户ID: {user_id}"

            updated_count = 0

            # 根据模式确定匹配字段
            if self.mode == 'user':
                # 用户模式：根据username和fullname匹配
                for _, exported_row in exported_data.iterrows():
                    mask = (
                        (self.merged_data['username'] == exported_row['username']) &
                        (self.merged_data['fullname'] == exported_row['fullname']) &
                        (self.merged_data['id'] == user_id) &
                        (self.merged_data['used'] == 0)
                    )

                    # 更新匹配的记录
                    if mask.any():
                        self.merged_data.loc[mask, 'used'] = 1
                        updated_count += mask.sum()

            else:  # link mode
                # 链接模式：根据url匹配
                for _, exported_row in exported_data.iterrows():
                    mask = (
                        (self.merged_data['url'] == exported_row['url']) &
                        (self.merged_data['id'] == user_id) &
                        (self.merged_data['used'] == 0)
                    )

                    # 更新匹配的记录
                    if mask.any():
                        self.merged_data.loc[mask, 'used'] = 1
                        updated_count += mask.sum()

            if updated_count > 0:
                logger.info(f"成功标记 {updated_count} 条任务为已完成")
                return True, f"成功标记 {updated_count} 条任务为已完成"
            else:
                return False, "没有找到匹配的任务记录"

        except Exception as e:
            error_msg = f"标记任务完成失败: {str(e)}"
            logger.error(error_msg)
            return False, error_msg

    def get_user_task_summary(self, user_id: int) -> Dict[str, int]:
        """
        获取指定用户的任务摘要

        Args:
            user_id: 用户ID

        Returns:
            任务摘要字典
        """
        if self.merged_data is None or self.merged_data.empty:
            return {'total': 0, 'completed': 0, 'available': 0}

        user_data = self.merged_data[self.merged_data['id'] == user_id]

        total = len(user_data)
        completed = len(user_data[user_data['used'] == 1])
        available = len(user_data[user_data['used'] == 0])

        return {
            'total': total,
            'completed': completed,
            'available': available
        }
    
    def _format_allocation_stats(self, stats: Dict[str, Any]) -> str:
        """格式化分配统计信息"""
        if not stats:
            return "无统计信息"
        
        lines = []
        for user_id in [1, 2, 3, 4, 5]:
            key = f'ID_{user_id}'
            if key in stats:
                count = stats[key]['count']
                percentage = stats[key]['percentage']
                lines.append(f"ID {user_id}: {count} 条 ({percentage}%)")
        
        if 'balance_score' in stats:
            lines.append(f"平衡度评分: {stats['balance_score']} (越小越平衡)")
        
        return '\n'.join(lines)

def create_csv_processor(mode: str) -> CSVProcessor:
    """创建CSV处理器实例"""
    return CSVProcessor(mode)
