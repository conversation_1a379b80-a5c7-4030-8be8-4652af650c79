#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据模型定义
包括用户查重和链接查重的数据结构定义
"""

import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
import logging
from pathlib import Path
from error_handler import DataValidator, ErrorHandler, validate_and_clean_data

logger = logging.getLogger(__name__)

class DataModel:
    """数据模型基类"""
    
    def __init__(self):
        self.mother_columns = []
        self.child_columns = []
        self.unique_key_columns = []
    
    def validate_mother_data(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """验证母体数据格式"""
        if df is None or df.empty:
            return False, "数据为空"
        
        # 检查必需列
        missing_cols = [col for col in self.mother_columns if col not in df.columns]
        if missing_cols:
            return False, f"缺少必需列: {', '.join(missing_cols)}"
        
        return True, "验证通过"
    
    def validate_child_data(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """验证子体数据格式"""
        if df is None or df.empty:
            return False, "数据为空"
        
        # 检查必需列
        missing_cols = [col for col in self.child_columns if col not in df.columns]
        if missing_cols:
            return False, f"缺少必需列: {', '.join(missing_cols)}"
        
        return True, "验证通过"
    
    def get_unique_key(self, row: pd.Series) -> tuple:
        """获取唯一键"""
        return tuple(row[col] for col in self.unique_key_columns)

class UserDataModel(DataModel):
    """用户查重数据模型"""
    
    def __init__(self):
        super().__init__()
        self.mother_columns = ['username', 'fullname', 'used', 'id']
        self.child_columns = ['username', 'fullname']
        self.unique_key_columns = ['username', 'fullname']
    
    def validate_mother_data(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """验证用户母体数据"""
        is_valid, message = super().validate_mother_data(df)
        if not is_valid:
            return is_valid, message
        
        # 检查used列的值
        if not df['used'].isin([0, 1]).all():
            return False, "used列只能包含0或1"
        
        # 检查id列的值
        if not df['id'].isin([1, 2, 3, 4, 5]).all():
            return False, "id列只能包含1-5的整数"
        
        # 检查唯一键重复
        unique_keys = df[self.unique_key_columns].drop_duplicates()
        if len(unique_keys) != len(df):
            return False, "存在重复的(username, fullname)组合"
        
        return True, "验证通过"
    
    def create_empty_mother_data(self) -> pd.DataFrame:
        """创建空的母体数据框"""
        return pd.DataFrame(columns=self.mother_columns)
    
    def merge_child_to_mother(self, mother_df: pd.DataFrame, child_df: pd.DataFrame) -> pd.DataFrame:
        """将子体数据合并到母体数据"""
        if mother_df is None or mother_df.empty:
            # 如果母体为空，创建新的母体数据
            new_data = child_df.copy()
            new_data['used'] = 0
            new_data['id'] = 0  # 临时设置为0，后续分配
            return new_data
        
        # 获取母体中已存在的唯一键
        existing_keys = set()
        for _, row in mother_df.iterrows():
            key = self.get_unique_key(row)
            existing_keys.add(key)
        
        # 筛选出不存在的子体数据
        new_rows = []
        for _, row in child_df.iterrows():
            key = self.get_unique_key(row)
            if key not in existing_keys:
                new_row = {
                    'username': row['username'],
                    'fullname': row['fullname'],
                    'used': 0,
                    'id': 0  # 临时设置为0，后续分配
                }
                new_rows.append(new_row)
        
        if new_rows:
            new_df = pd.DataFrame(new_rows)
            result_df = pd.concat([mother_df, new_df], ignore_index=True)
        else:
            result_df = mother_df.copy()
        
        return result_df

class LinkDataModel(DataModel):
    """链接查重数据模型"""
    
    def __init__(self):
        super().__init__()
        self.mother_columns = ['url', 'used', 'id']
        self.child_columns = ['url']
        self.unique_key_columns = ['url']
    
    def validate_mother_data(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """验证链接母体数据"""
        is_valid, message = super().validate_mother_data(df)
        if not is_valid:
            return is_valid, message
        
        # 检查used列的值
        if not df['used'].isin([0, 1]).all():
            return False, "used列只能包含0或1"
        
        # 检查id列的值
        if not df['id'].isin([1, 2, 3, 4, 5]).all():
            return False, "id列只能包含1-5的整数"
        
        # 检查URL重复
        if df['url'].duplicated().any():
            return False, "存在重复的URL"
        
        return True, "验证通过"
    
    def create_empty_mother_data(self) -> pd.DataFrame:
        """创建空的母体数据框"""
        return pd.DataFrame(columns=self.mother_columns)
    
    def merge_child_to_mother(self, mother_df: pd.DataFrame, child_df: pd.DataFrame) -> pd.DataFrame:
        """将子体数据合并到母体数据"""
        if mother_df is None or mother_df.empty:
            # 如果母体为空，创建新的母体数据
            new_data = child_df.copy()
            new_data['used'] = 0
            new_data['id'] = 0  # 临时设置为0，后续分配
            return new_data
        
        # 获取母体中已存在的URL
        existing_urls = set(mother_df['url'].tolist())
        
        # 筛选出不存在的子体数据
        new_rows = []
        for _, row in child_df.iterrows():
            if row['url'] not in existing_urls:
                new_row = {
                    'url': row['url'],
                    'used': 0,
                    'id': 0  # 临时设置为0，后续分配
                }
                new_rows.append(new_row)
        
        if new_rows:
            new_df = pd.DataFrame(new_rows)
            result_df = pd.concat([mother_df, new_df], ignore_index=True)
        else:
            result_df = mother_df.copy()
        
        return result_df

def get_data_model(mode: str) -> DataModel:
    """根据模式获取对应的数据模型"""
    if mode == 'user':
        return UserDataModel()
    elif mode == 'link':
        return LinkDataModel()
    else:
        raise ValueError(f"不支持的模式: {mode}")

def auto_map_columns(df: pd.DataFrame, expected_columns: List[str], mode: str) -> Tuple[pd.DataFrame, List[str]]:
    """
    自动映射CSV列名
    如果列名不匹配或没有列名，按默认顺序映射

    Args:
        df: 原始DataFrame
        expected_columns: 期望的列名列表
        mode: 模式 ('user' 或 'link')

    Returns:
        (映射后的DataFrame, 警告信息列表)
    """
    warnings = []
    result_df = df.copy()

    # 检查是否有列名匹配
    matching_columns = [col for col in expected_columns if col in df.columns]

    if len(matching_columns) == len(expected_columns):
        # 所有列名都匹配，直接返回
        logger.info("CSV列名完全匹配，无需映射")
        return result_df[expected_columns], warnings

    elif len(matching_columns) > 0:
        # 部分列名匹配，提示用户
        missing_columns = [col for col in expected_columns if col not in df.columns]
        warnings.append(f"部分列名匹配，缺少列: {', '.join(missing_columns)}")
        logger.warning(f"部分列名匹配，缺少列: {missing_columns}")

    # 如果列数不够，返回错误
    if len(df.columns) < len(expected_columns):
        error_msg = f"CSV文件列数不足，需要{len(expected_columns)}列，实际只有{len(df.columns)}列"
        logger.error(error_msg)
        return None, [error_msg]

    # 自动映射：按顺序将前N列映射到期望的列名
    logger.info(f"自动映射CSV列名：{list(df.columns[:len(expected_columns)])} -> {expected_columns}")

    # 创建新的DataFrame，使用期望的列名
    mapped_data = {}
    for i, expected_col in enumerate(expected_columns):
        if i < len(df.columns):
            mapped_data[expected_col] = df.iloc[:, i]

    result_df = pd.DataFrame(mapped_data)

    # 添加映射信息到警告
    original_columns = list(df.columns[:len(expected_columns)])
    mapping_info = " -> ".join([f"{orig}→{exp}" for orig, exp in zip(original_columns, expected_columns)])
    warnings.append(f"已自动映射列名: {mapping_info}")

    return result_df, warnings

@ErrorHandler.handle_file_error
def load_csv_file(file_path: str) -> Tuple[Optional[pd.DataFrame], str]:
    """加载CSV文件"""
    # 验证文件路径
    is_valid, message = DataValidator.validate_file_path(file_path)
    if not is_valid:
        return None, message

    try:
        # 尝试UTF-8编码
        df = pd.read_csv(file_path, encoding='utf-8')
        logger.info(f"成功加载CSV文件: {file_path}, 行数: {len(df)}")
        return df, "文件加载成功"
    except UnicodeDecodeError:
        try:
            # 尝试GBK编码
            df = pd.read_csv(file_path, encoding='gbk')
            logger.info(f"使用GBK编码成功加载CSV文件: {file_path}, 行数: {len(df)}")
            return df, "文件加载成功"
        except Exception as e:
            logger.error(f"加载CSV文件失败: {file_path}, 错误: {str(e)}")
            return None, f"文件加载失败: {str(e)}"
    except Exception as e:
        logger.error(f"加载CSV文件失败: {file_path}, 错误: {str(e)}")
        return None, f"文件加载失败: {str(e)}"

@ErrorHandler.handle_file_error
def save_csv_file(df: pd.DataFrame, file_path: str) -> Tuple[bool, str]:
    """保存CSV文件"""
    try:
        if df is None or df.empty:
            return False, "数据为空，无法保存"

        # 确保目录存在
        Path(file_path).parent.mkdir(parents=True, exist_ok=True)

        df.to_csv(file_path, index=False, encoding='utf-8-sig')
        logger.info(f"成功保存CSV文件: {file_path}, 行数: {len(df)}")
        return True, "文件保存成功"
    except Exception as e:
        logger.error(f"保存CSV文件失败: {file_path}, 错误: {str(e)}")
        return False, f"文件保存失败: {str(e)}"
