#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最简单的PyInstaller打包脚本
用于将wowcker_tool打包为exe文件
"""

import os
import sys

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")

    if version < (3, 7):
        print("❌ Python版本过低，需要Python 3.7或更高版本")
        return False
    elif version < (3, 8):
        print("⚠️  建议使用Python 3.8或更高版本以获得更好的兼容性")
    else:
        print("✅ Python版本符合要求")

    return True

def check_dependencies():
    """检查依赖"""
    print("\n检查依赖模块...")

    # 检查tkinter
    try:
        import tkinter
        print("✅ tkinter 可用")
    except ImportError:
        print("❌ tkinter 不可用，请检查Python安装")
        return False

    # 检查pandas
    try:
        import pandas
        print(f"✅ pandas {pandas.__version__} 可用")
    except ImportError:
        print("❌ pandas 未安装，请运行: pip install pandas")
        return False

    # 检查pyinstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller 可用")
    except ImportError:
        print("❌ PyInstaller 未安装，请运行: pip install pyinstaller")
        return False

    return True

def main():
    """主函数"""
    print("=" * 50)
    print("wowcker_tool - 简单打包脚本")
    print("=" * 50)

    # 检查Python版本
    if not check_python_version():
        input("按回车键退出...")
        return

    # 检查依赖
    if not check_dependencies():
        print("\n请先安装缺失的依赖，然后重新运行此脚本")
        input("按回车键退出...")
        return

    print("\n项目信息:")
    print("- 主要依赖: pandas, tkinter (内置)")
    print("- 主程序文件: main.py")
    print()

    # 清理之前的构建文件
    if os.path.exists("dist"):
        import shutil
        shutil.rmtree("dist")
        print("✅ 清理旧的构建文件")

    if os.path.exists("build"):
        import shutil
        shutil.rmtree("build")
        print("✅ 清理旧的临时文件")

    # 删除spec文件
    for file in os.listdir("."):
        if file.endswith(".spec"):
            os.remove(file)
            print(f"✅ 删除旧的spec文件: {file}")

    print()
    print("开始打包...")
    print("命令: pyinstaller --onefile --windowed --name=wowcker_tool main.py")
    print()

    # 使用os.system执行命令（更简单直接）
    result = os.system("pyinstaller --onefile --windowed --name=wowcker_tool main.py")

    print()
    if result == 0:
        print("🎉 打包成功！")
        print("📁 exe文件位置: dist/wowcker_tool.exe")

        # 检查文件是否存在
        exe_path = "dist/wowcker_tool.exe"
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
            print(f"📄 文件大小: {file_size:.1f} MB")
        else:
            print("⚠️  exe文件未找到，请检查dist目录")
    else:
        print("❌ 打包失败")
        print("请检查:")
        print("1. 是否安装了pyinstaller: pip install pyinstaller")
        print("2. 是否安装了所有依赖: pip install -r requirements.txt")
        print("3. main.py文件是否存在")

    print()
    print("=" * 50)

if __name__ == "__main__":
    main()
    input("按回车键退出...")
