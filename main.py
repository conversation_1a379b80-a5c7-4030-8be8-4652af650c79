#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
wowcker_tool主程序
支持用户查重和链接查重功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import os
from pathlib import Path
from typing import Optional, List, Dict, Any
import logging
from csv_processor import create_csv_processor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataDeduplicationTool:
    """wowcker_tool主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("wowcker_tool")
        self.root.geometry("900x700")
        self.root.resizable(True, True)

        # 当前用户ID
        self.current_user_id = None
        # 当前模式：'user' 或 'link'
        self.current_mode = None
        # CSV处理器
        self.csv_processor = None

        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="wowcker_tool", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # 显示主页
        self.show_home_page(main_frame)
    
    def show_home_page(self, parent):
        """显示主页选择界面"""
        self.clear_frame(parent)
        
        # 功能选择标题
        select_label = ttk.Label(parent, text="请选择功能模式：", font=("Arial", 12))
        select_label.grid(row=1, column=0, pady=(0, 20))
        
        # 按钮框架
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=2, column=0, pady=20)
        
        # 用户查重按钮
        user_btn = ttk.Button(button_frame, text="用户查重", 
                             command=lambda: self.select_mode('user'),
                             width=15)
        user_btn.grid(row=0, column=0, padx=10)
        
        # 链接查重按钮
        link_btn = ttk.Button(button_frame, text="链接查重", 
                             command=lambda: self.select_mode('link'),
                             width=15)
        link_btn.grid(row=0, column=1, padx=10)
    
    def select_mode(self, mode):
        """选择功能模式"""
        self.current_mode = mode
        # 创建对应的CSV处理器
        self.csv_processor = create_csv_processor(mode)
        self.show_login_page()
    
    def show_login_page(self):
        """显示登录页面"""
        # 创建新窗口
        login_window = tk.Toplevel(self.root)
        login_window.title("用户登录")
        login_window.geometry("300x200")
        login_window.resizable(False, False)
        
        # 居中显示
        login_window.transient(self.root)
        login_window.grab_set()
        
        # 登录框架
        login_frame = ttk.Frame(login_window, padding="20")
        login_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(login_frame, text=f"{'用户查重' if self.current_mode == 'user' else '链接查重'} - 登录", 
                               font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 20))
        
        # ID输入
        id_label = ttk.Label(login_frame, text="请输入您的ID (1-5):")
        id_label.pack(pady=(0, 10))
        
        id_var = tk.StringVar()
        id_entry = ttk.Entry(login_frame, textvariable=id_var, width=20)
        id_entry.pack(pady=(0, 20))
        id_entry.focus()
        
        # 登录按钮
        def login():
            user_id = id_var.get().strip()
            if self.validate_user_id(user_id):
                self.current_user_id = int(user_id)
                login_window.destroy()
                self.show_main_interface()
            else:
                messagebox.showerror("错误", "请输入有效的ID (1-5)")
        
        login_btn = ttk.Button(login_frame, text="登录", command=login)
        login_btn.pack(pady=10)
        
        # 绑定回车键
        login_window.bind('<Return>', lambda e: login())
    
    def validate_user_id(self, user_id):
        """验证用户ID"""
        try:
            id_num = int(user_id)
            return 1 <= id_num <= 5
        except ValueError:
            return False
    
    def show_main_interface(self):
        """显示主功能界面"""
        # 清空主窗口
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # 重新设置主界面
        self.setup_main_interface()
    
    def setup_main_interface(self):
        """设置主功能界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 顶部信息栏
        info_frame = ttk.Frame(main_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        mode_text = "用户查重" if self.current_mode == 'user' else "链接查重"
        info_label = ttk.Label(info_frame, text=f"当前模式: {mode_text} | 用户ID: {self.current_user_id}", 
                              font=("Arial", 10))
        info_label.pack(side=tk.LEFT)
        
        # 返回主页按钮
        home_btn = ttk.Button(info_frame, text="返回主页", command=self.return_home)
        home_btn.pack(side=tk.RIGHT)
        
        # 分隔线
        separator = ttk.Separator(main_frame, orient='horizontal')
        separator.pack(fill=tk.X, pady=10)
        
        # 功能区域
        self.setup_function_area(main_frame)
    
    def setup_function_area(self, parent):
        """设置功能区域"""
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 数据导入标签页
        import_frame = ttk.Frame(notebook)
        notebook.add(import_frame, text="数据导入")
        self.setup_import_tab(import_frame)
        
        # 数据管理标签页
        manage_frame = ttk.Frame(notebook)
        notebook.add(manage_frame, text="数据管理")
        self.setup_manage_tab(manage_frame)
        
        # 任务操作标签页
        task_frame = ttk.Frame(notebook)
        notebook.add(task_frame, text="任务操作")
        self.setup_task_tab(task_frame)
    
    def setup_import_tab(self, parent):
        """设置数据导入标签页"""
        # 创建滚动框架
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 母体文件导入区域
        mother_frame = ttk.LabelFrame(scrollable_frame, text="母体文件导入", padding="10")
        mother_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(mother_frame, text="选择母体CSV文件（只能导入一个）:").pack(anchor=tk.W)

        mother_btn_frame = ttk.Frame(mother_frame)
        mother_btn_frame.pack(fill=tk.X, pady=5)

        ttk.Button(mother_btn_frame, text="选择母体文件",
                  command=self.import_mother_file).pack(side=tk.LEFT)

        self.mother_status_label = ttk.Label(mother_btn_frame, text="未导入", foreground="red")
        self.mother_status_label.pack(side=tk.LEFT, padx=(10, 0))

        # 子体文件导入区域
        child_frame = ttk.LabelFrame(scrollable_frame, text="子体文件导入", padding="10")
        child_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(child_frame, text="选择子体CSV文件（可以导入多个）:").pack(anchor=tk.W)

        child_btn_frame = ttk.Frame(child_frame)
        child_btn_frame.pack(fill=tk.X, pady=5)

        ttk.Button(child_btn_frame, text="添加子体文件",
                  command=self.import_child_file).pack(side=tk.LEFT)
        ttk.Button(child_btn_frame, text="清空子体文件",
                  command=self.clear_child_files).pack(side=tk.LEFT, padx=(5, 0))

        # 子体文件列表
        self.child_files_text = tk.Text(child_frame, height=6, width=60)
        child_scroll = ttk.Scrollbar(child_frame, orient="vertical", command=self.child_files_text.yview)
        self.child_files_text.configure(yscrollcommand=child_scroll.set)

        text_frame = ttk.Frame(child_frame)
        text_frame.pack(fill=tk.X, pady=5)
        self.child_files_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        child_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 数据合并区域
        merge_frame = ttk.LabelFrame(scrollable_frame, text="数据合并与分配", padding="10")
        merge_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(merge_frame, text="合并数据并分配ID",
                  command=self.merge_and_allocate_data).pack(pady=5)

        self.merge_status_text = tk.Text(merge_frame, height=8, width=60)
        merge_scroll = ttk.Scrollbar(merge_frame, orient="vertical", command=self.merge_status_text.yview)
        self.merge_status_text.configure(yscrollcommand=merge_scroll.set)

        merge_text_frame = ttk.Frame(merge_frame)
        merge_text_frame.pack(fill=tk.X, pady=5)
        self.merge_status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        merge_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_manage_tab(self, parent):
        """设置数据管理标签页"""
        # 数据统计区域
        stats_frame = ttk.LabelFrame(parent, text="数据统计", padding="10")
        stats_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(stats_frame, text="刷新统计", command=self.refresh_statistics).pack(pady=5)

        self.stats_text = tk.Text(stats_frame, height=10, width=60)
        stats_scroll = ttk.Scrollbar(stats_frame, orient="vertical", command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scroll.set)

        stats_text_frame = ttk.Frame(stats_frame)
        stats_text_frame.pack(fill=tk.X, pady=5)
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 数据导出区域
        export_frame = ttk.LabelFrame(parent, text="数据导出", padding="10")
        export_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(export_frame, text="导出完整母体数据",
                  command=self.export_mother_data).pack(pady=5)
    
    def setup_task_tab(self, parent):
        """设置任务操作标签页"""
        # 任务领取区域
        task_frame = ttk.LabelFrame(parent, text="任务领取", padding="10")
        task_frame.pack(fill=tk.X, padx=10, pady=5)

        # 当前用户信息
        user_info_label = ttk.Label(task_frame, text=f"当前用户ID: {self.current_user_id}",
                                   font=("Arial", 10, "bold"))
        user_info_label.pack(pady=5)

        # 任务领取说明
        mode_text = "用户名和全名" if self.current_mode == 'user' else "链接URL"
        desc_label = ttk.Label(task_frame,
                              text=f"点击下方按钮领取属于您的任务（包含{mode_text}数据）")
        desc_label.pack(pady=5)

        ttk.Button(task_frame, text="领取我的任务",
                  command=self.claim_user_tasks).pack(pady=5)

        # 手动标记完成按钮
        ttk.Button(task_frame, text="手动标记任务完成",
                  command=self.manual_mark_completed).pack(pady=5)

        # 任务统计区域
        task_stats_frame = ttk.LabelFrame(parent, text="任务统计", padding="10")
        task_stats_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Button(task_stats_frame, text="查看任务统计",
                  command=self.show_task_statistics).pack(pady=5)

        self.task_stats_text = tk.Text(task_stats_frame, height=8, width=60)
        task_stats_scroll = ttk.Scrollbar(task_stats_frame, orient="vertical",
                                         command=self.task_stats_text.yview)
        self.task_stats_text.configure(yscrollcommand=task_stats_scroll.set)

        task_stats_text_frame = ttk.Frame(task_stats_frame)
        task_stats_text_frame.pack(fill=tk.X, pady=5)
        self.task_stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        task_stats_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def return_home(self):
        """返回主页"""
        self.current_user_id = None
        self.current_mode = None
        self.csv_processor = None

        # 重新初始化界面
        for widget in self.root.winfo_children():
            widget.destroy()
        self.setup_ui()

    # 数据导入相关方法
    def import_mother_file(self):
        """导入母体文件"""
        if not self.csv_processor:
            messagebox.showerror("错误", "CSV处理器未初始化")
            return

        file_path = filedialog.askopenfilename(
            title="选择母体CSV文件",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            success, message = self.csv_processor.import_mother_file(file_path)
            if success:
                self.mother_status_label.config(text="已导入", foreground="green")
                messagebox.showinfo("成功", message)
            else:
                self.mother_status_label.config(text="导入失败", foreground="red")
                messagebox.showerror("错误", message)

    def import_child_file(self):
        """导入子体文件"""
        if not self.csv_processor:
            messagebox.showerror("错误", "CSV处理器未初始化")
            return

        file_path = filedialog.askopenfilename(
            title="选择子体CSV文件",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            success, message = self.csv_processor.import_child_file(file_path)
            if success:
                messagebox.showinfo("成功", message)
                self.update_child_files_display()
            else:
                messagebox.showerror("错误", message)

    def clear_child_files(self):
        """清空子体文件"""
        if not self.csv_processor:
            return

        result = messagebox.askyesno("确认", "确定要清空所有子体文件吗？")
        if result:
            self.csv_processor.clear_child_data()
            self.update_child_files_display()
            messagebox.showinfo("成功", "已清空所有子体文件")

    def update_child_files_display(self):
        """更新子体文件显示"""
        if not self.csv_processor:
            return

        summary = self.csv_processor.get_data_summary()
        self.child_files_text.delete(1.0, tk.END)

        if summary['child_files']:
            for i, filename in enumerate(summary['child_files'], 1):
                self.child_files_text.insert(tk.END, f"{i}. {filename}\n")
            self.child_files_text.insert(tk.END, f"\n总计: {summary['child_files_count']} 个文件")
            self.child_files_text.insert(tk.END, f", {summary['child_data_total']} 条数据")
        else:
            self.child_files_text.insert(tk.END, "暂无子体文件")

    def merge_and_allocate_data(self):
        """合并数据并分配ID"""
        if not self.csv_processor:
            messagebox.showerror("错误", "CSV处理器未初始化")
            return

        # 检查是否有数据可合并
        summary = self.csv_processor.get_data_summary()
        if not summary['has_mother_data'] and summary['child_files_count'] == 0:
            messagebox.showwarning("警告", "请先导入母体文件或子体文件")
            return

        try:
            success, message = self.csv_processor.merge_and_allocate_data()

            self.merge_status_text.delete(1.0, tk.END)
            self.merge_status_text.insert(tk.END, message)

            if success:
                messagebox.showinfo("成功", "数据合并和分配完成！")
            else:
                messagebox.showerror("错误", message)

        except Exception as e:
            error_msg = f"合并数据时发生错误: {str(e)}"
            logger.error(error_msg)
            messagebox.showerror("错误", error_msg)
    
    # 数据管理相关方法
    def refresh_statistics(self):
        """刷新统计信息"""
        if not self.csv_processor:
            messagebox.showerror("错误", "CSV处理器未初始化")
            return

        summary = self.csv_processor.get_data_summary()

        self.stats_text.delete(1.0, tk.END)

        # 基本统计
        self.stats_text.insert(tk.END, "=== 数据统计 ===\n")
        self.stats_text.insert(tk.END, f"母体数据: {'已导入' if summary['has_mother_data'] else '未导入'}\n")
        self.stats_text.insert(tk.END, f"母体数据行数: {summary['mother_data_count']}\n")
        self.stats_text.insert(tk.END, f"子体文件数量: {summary['child_files_count']}\n")
        self.stats_text.insert(tk.END, f"子体数据总行数: {summary['child_data_total']}\n")
        self.stats_text.insert(tk.END, f"合并后数据: {'已生成' if summary['has_merged_data'] else '未生成'}\n")
        self.stats_text.insert(tk.END, f"合并后数据行数: {summary['merged_data_count']}\n\n")

        # 分配统计
        if 'allocation_stats' in summary:
            stats = summary['allocation_stats']
            self.stats_text.insert(tk.END, "=== 分配统计 ===\n")
            for user_id in [1, 2, 3, 4, 5]:
                key = f'ID_{user_id}'
                if key in stats:
                    count = stats[key]['count']
                    percentage = stats[key]['percentage']
                    self.stats_text.insert(tk.END, f"ID {user_id}: {count} 条 ({percentage}%)\n")

            if 'balance_score' in stats:
                self.stats_text.insert(tk.END, f"\n平衡度评分: {stats['balance_score']} (越小越平衡)\n")

    def export_mother_data(self):
        """导出母体数据"""
        if not self.csv_processor:
            messagebox.showerror("错误", "CSV处理器未初始化")
            return

        summary = self.csv_processor.get_data_summary()
        if not summary['has_merged_data']:
            messagebox.showwarning("警告", "请先合并数据后再导出")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存母体数据",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            success, message = self.csv_processor.export_mother_data(file_path)
            if success:
                messagebox.showinfo("成功", message)
            else:
                messagebox.showerror("错误", message)

    # 任务操作相关方法
    def claim_user_tasks(self):
        """领取用户任务"""
        if not self.csv_processor:
            messagebox.showerror("错误", "CSV处理器未初始化")
            return

        summary = self.csv_processor.get_data_summary()
        if not summary['has_merged_data']:
            messagebox.showwarning("警告", "请先合并数据后再领取任务")
            return

        # 检查是否有可领取的任务
        task_summary = self.csv_processor.get_user_task_summary(self.current_user_id)
        if task_summary['available'] == 0:
            messagebox.showinfo("提示", f"用户ID {self.current_user_id} 没有可领取的任务")
            return

        # 显示数量设置对话框
        limit = self.show_task_limit_dialog(task_summary['available'])
        if limit is None:  # 用户取消了操作
            return

        file_path = filedialog.asksaveasfilename(
            title=f"保存用户ID {self.current_user_id} 的任务数据",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            success, message = self.csv_processor.export_user_tasks(self.current_user_id, file_path, limit)
            if success:
                # 导出成功后，询问是否标记为完成
                self.show_task_completion_dialog(file_path)
            else:
                messagebox.showerror("错误", message)

    def show_task_limit_dialog(self, available_count):
        """显示任务数量设置对话框"""
        # 创建对话框窗口
        dialog = tk.Toplevel(self.root)
        dialog.title("设置领取数量")
        dialog.geometry("400x220")
        dialog.resizable(False, False)

        # 设置为最顶层并居中显示
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.attributes('-topmost', True)

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # 对话框内容
        dialog_frame = ttk.Frame(dialog, padding="20")
        dialog_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(dialog_frame, text="设置领取任务数量", font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 10))

        # 说明文字
        info_label = ttk.Label(dialog_frame,
                              text=f"您有 {available_count} 个可领取的任务\n请设置要领取的数量:",
                              justify=tk.CENTER)
        info_label.pack(pady=(0, 15))

        # 数量输入框架
        input_frame = ttk.Frame(dialog_frame)
        input_frame.pack(pady=10)

        ttk.Label(input_frame, text="领取数量:").pack(side=tk.LEFT, padx=(0, 5))

        # 数量输入框
        limit_var = tk.StringVar(value=str(available_count))
        limit_entry = ttk.Entry(input_frame, textvariable=limit_var, width=10)
        limit_entry.pack(side=tk.LEFT, padx=5)

        ttk.Label(input_frame, text=f"(最多 {available_count} 个)").pack(side=tk.LEFT, padx=(5, 0))

        # 按钮框架
        button_frame = ttk.Frame(dialog_frame)
        button_frame.pack(pady=15)

        result = {'limit': None}

        def confirm():
            try:
                limit = int(limit_var.get())
                if limit <= 0:
                    messagebox.showerror("错误", "请输入大于0的数量")
                    return
                if limit > available_count:
                    messagebox.showerror("错误", f"输入的数量不能超过可领取的任务数量({available_count})")
                    return
                result['limit'] = limit
                dialog.destroy()
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数字")

        def cancel():
            dialog.destroy()

        # 确认和取消按钮
        ttk.Button(button_frame, text="确认", command=confirm, width=12).pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="取消", command=cancel, width=12).pack(side=tk.LEFT, padx=10)

        # 设置焦点和回车键绑定
        limit_entry.focus()
        limit_entry.bind('<Return>', lambda e: confirm())
        dialog.bind('<Escape>', lambda e: cancel())

        # 等待对话框关闭
        dialog.wait_window()

        return result['limit']

    def show_task_completion_dialog(self, exported_file_path):
        """显示任务完成标记对话框"""
        # 创建对话框窗口
        dialog = tk.Toplevel(self.root)
        dialog.title("任务状态")
        dialog.geometry("450x250")
        dialog.resizable(False, False)

        # 设置为最顶层并居中显示
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.attributes('-topmost', True)

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # 对话框内容
        dialog_frame = ttk.Frame(dialog, padding="20")
        dialog_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(dialog_frame, text="任务导出完成", font=("Arial", 12, "bold"))
        title_label.pack(pady=(0, 10))

        # 说明文字
        info_label = ttk.Label(dialog_frame,
                              text=f"您的任务已成功导出到:\n{exported_file_path}\n\n请选择任务状态:",
                              justify=tk.CENTER, wraplength=400)
        info_label.pack(pady=(0, 20))

        # 按钮框架
        button_frame = ttk.Frame(dialog_frame)
        button_frame.pack(pady=15)

        # 完成按钮
        def mark_completed():
            dialog.destroy()
            self.mark_tasks_as_completed(exported_file_path)

        # 未完成按钮
        def mark_not_completed():
            dialog.destroy()
            messagebox.showinfo("提示", "任务状态保持为未完成，您可以稍后再次标记为完成")

        completed_btn = ttk.Button(button_frame, text="标记为完成",
                                  command=mark_completed, width=15)
        completed_btn.pack(side=tk.LEFT, padx=10)

        not_completed_btn = ttk.Button(button_frame, text="保持未完成",
                                      command=mark_not_completed, width=15)
        not_completed_btn.pack(side=tk.LEFT, padx=10)

        # 默认焦点在"保持未完成"按钮上
        not_completed_btn.focus()

    def mark_tasks_as_completed(self, exported_file_path):
        """标记任务为已完成"""
        try:
            # 读取导出的文件
            from data_models import load_csv_file
            exported_data, load_message = load_csv_file(exported_file_path)

            if exported_data is None:
                messagebox.showerror("错误", f"无法读取导出文件: {load_message}")
                return

            # 标记任务为完成
            success, message = self.csv_processor.mark_tasks_completed(self.current_user_id, exported_data)

            if success:
                messagebox.showinfo("成功", f"{message}\n\n母体数据已更新，相关任务已标记为完成。")
                # 刷新统计信息
                self.show_task_statistics()
            else:
                messagebox.showerror("错误", f"标记任务完成失败: {message}")

        except Exception as e:
            error_msg = f"标记任务完成时发生错误: {str(e)}"
            messagebox.showerror("错误", error_msg)

    def manual_mark_completed(self):
        """手动标记任务完成"""
        if not self.csv_processor:
            messagebox.showerror("错误", "CSV处理器未初始化")
            return

        summary = self.csv_processor.get_data_summary()
        if not summary['has_merged_data']:
            messagebox.showwarning("警告", "请先合并数据后再标记任务")
            return

        # 选择要标记完成的CSV文件
        file_path = filedialog.askopenfilename(
            title="选择要标记为完成的任务CSV文件",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            # 确认对话框
            result = messagebox.askyesno(
                "确认标记",
                f"确定要将文件中的所有任务标记为完成吗？\n\n文件: {file_path}\n\n此操作将更新母体数据中对应记录的used字段为1。"
            )

            if result:
                self.mark_tasks_as_completed(file_path)

    def show_task_statistics(self):
        """显示任务统计"""
        if not self.csv_processor:
            messagebox.showerror("错误", "CSV处理器未初始化")
            return

        summary = self.csv_processor.get_data_summary()

        self.task_stats_text.delete(1.0, tk.END)

        if not summary['has_merged_data']:
            self.task_stats_text.insert(tk.END, "请先合并数据后查看任务统计")
            return

        # 获取详细的任务统计
        if 'allocation_stats' in summary and self.csv_processor.merged_data is not None:
            df = self.csv_processor.merged_data

            self.task_stats_text.insert(tk.END, "=== 全体任务统计 ===\n")

            total_all_tasks = len(df)
            total_available = len(df[df['used'] == 0])
            total_completed = len(df[df['used'] == 1])
            completion_rate = (total_completed / total_all_tasks * 100) if total_all_tasks > 0 else 0

            self.task_stats_text.insert(tk.END, f"总任务数: {total_all_tasks}\n")
            self.task_stats_text.insert(tk.END, f"已完成: {total_completed} ({completion_rate:.1f}%)\n")
            self.task_stats_text.insert(tk.END, f"未完成: {total_available} ({100-completion_rate:.1f}%)\n\n")

            self.task_stats_text.insert(tk.END, "=== 各用户任务统计 ===\n")

            for user_id in [1, 2, 3, 4, 5]:
                total_tasks = len(df[df['id'] == user_id])
                available_tasks = len(df[(df['id'] == user_id) & (df['used'] == 0)])
                completed_tasks = len(df[(df['id'] == user_id) & (df['used'] == 1)])
                user_completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0

                self.task_stats_text.insert(tk.END, f"ID {user_id}:\n")
                self.task_stats_text.insert(tk.END, f"  总任务: {total_tasks}\n")
                self.task_stats_text.insert(tk.END, f"  已完成: {completed_tasks} ({user_completion_rate:.1f}%)\n")
                self.task_stats_text.insert(tk.END, f"  可领取: {available_tasks}\n\n")

            # 当前用户的详细任务信息
            current_summary = self.csv_processor.get_user_task_summary(self.current_user_id)
            current_completion_rate = (current_summary['completed'] / current_summary['total'] * 100) if current_summary['total'] > 0 else 0

            self.task_stats_text.insert(tk.END, f"=== 您的任务详情 (ID {self.current_user_id}) ===\n")
            self.task_stats_text.insert(tk.END, f"总任务数: {current_summary['total']}\n")
            self.task_stats_text.insert(tk.END, f"已完成: {current_summary['completed']} ({current_completion_rate:.1f}%)\n")
            self.task_stats_text.insert(tk.END, f"可领取: {current_summary['available']}\n")

            if current_summary['available'] > 0:
                self.task_stats_text.insert(tk.END, f"\n💡 您还有 {current_summary['available']} 个任务可以领取！")
            else:
                self.task_stats_text.insert(tk.END, f"\n🎉 恭喜！您已完成所有分配的任务！")

    def clear_frame(self, frame):
        """清空框架中的所有控件"""
        for widget in frame.winfo_children():
            widget.destroy()

    def run(self):
        """运行应用程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = DataDeduplicationTool()
    app.run()
